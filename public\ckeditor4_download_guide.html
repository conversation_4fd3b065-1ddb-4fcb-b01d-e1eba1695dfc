<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor 4 本地安装指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .code {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .info {
            background: #e3f2fd;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        h1, h2 { color: #333; }
        h3 { color: #555; }
        .download-links {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .download-links a {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .download-links a:hover {
            background: #005a8b;
        }
    </style>
</head>
<body>
    <h1>📦 CKEditor 4 本地安装指南</h1>
    
    <div class="success">
        <h3>✅ 当前状态</h3>
        <p>基本功能已经可以正常使用！现在需要下载完整版到本地，添加mention等高级功能。</p>
        <p><strong>测试页面：</strong><a href="/ckeditor4_simple_test.html">CKEditor 4 简化版测试</a></p>
    </div>

    <h2>📋 下载步骤</h2>

    <div class="step">
        <h3>步骤1：下载CKEditor 4完整版</h3>
        <p>有多种方式下载CKEditor 4：</p>
        
        <div class="download-links">
            <h4>官方下载链接：</h4>
            <a href="https://ckeditor.com/ckeditor-4/download/" target="_blank">官方下载页面</a>
            <a href="https://download.ckeditor.com/ckeditor/releases/ckeditor_4.22.1_full.zip" target="_blank">直接下载 Full 版本</a>
            <a href="https://github.com/ckeditor/ckeditor4/releases/tag/4.22.1" target="_blank">GitHub 发布页面</a>
        </div>

        <div class="warning">
            <h4>⚠️ 如果官方链接无法访问</h4>
            <p>可以使用以下备用方案：</p>
            <ul>
                <li>使用代理或VPN访问官方网站</li>
                <li>从GitHub镜像站下载</li>
                <li>使用国内CDN（但功能可能不完整）</li>
            </ul>
        </div>
    </div>

    <div class="step">
        <h3>步骤2：解压到项目目录</h3>
        <p>将下载的zip文件解压到以下目录：</p>
        <div class="code">
# 目标目录
d:\phpstudy_pro\WWW\opendelclub\public\static\ckeditor4\

# 解压后的结构应该是：
public/
└── static/
    └── ckeditor4/
        ├── ckeditor.js          # 主文件
        ├── config.js            # 配置文件
        ├── contents.css         # 样式文件
        ├── plugins/             # 插件目录
        │   ├── mentions/        # mention插件
        │   ├── autocomplete/    # 自动完成插件
        │   └── ...
        ├── skins/               # 皮肤目录
        ├── lang/                # 语言包目录
        └── ...
        </div>
    </div>

    <div class="step">
        <h3>步骤3：验证文件结构</h3>
        <p>确保以下关键文件存在：</p>
        <div class="code">
✅ public/static/ckeditor4/ckeditor.js
✅ public/static/ckeditor4/plugins/mentions/plugin.js
✅ public/static/ckeditor4/plugins/autocomplete/plugin.js
✅ public/static/ckeditor4/plugins/textmatch/plugin.js
✅ public/static/ckeditor4/lang/zh-cn.js
        </div>
    </div>

    <div class="step">
        <h3>步骤4：更新配置文件</h3>
        <p>下载完成后，更新配置文件以启用mention功能：</p>
        
        <h4>修改 ckeditor4.js：</h4>
        <div class="code">
// 启用mention相关插件
extraPlugins: 'mentions,autocomplete,textmatch,uploadimage,image2,colorbutton,colordialog,font,justify,tableresize,tabletools,specialchar,codesnippet,widget,lineutils,clipboard,notification',

// 添加mention配置
mentions: [
    {
        feed: function(opts, callback) {
            fetchMentionUsers(opts.query).then(users => {
                callback(users);
            }).catch(error => {
                console.error('Mention feed error:', error);
                callback([]);
            });
        },
        marker: '@',
        minChars: 0,
        itemTemplate: '&lt;li data-id="{id}"&gt;&lt;strong&gt;{name}&lt;/strong&gt;&lt;/li&gt;',
        outputTemplate: '&lt;a href="/iCommunity/user/{userId}" class="mention" data-mention="true" data-user-id="{userId}"&gt;@{name}&lt;/a&gt;&amp;nbsp;'
    }
]
        </div>
    </div>

    <div class="step">
        <h3>步骤5：更新模板引用</h3>
        <p>将模板中的CDN引用改为本地引用：</p>
        
        <h4>原来的引用：</h4>
        <div class="code">
&lt;script src="https://cdn.ckeditor.com/4.22.1/standard-all/ckeditor.js"&gt;&lt;/script&gt;
        </div>

        <h4>改为本地引用：</h4>
        <div class="code">
&lt;script src="/static/ckeditor4/ckeditor.js"&gt;&lt;/script&gt;
        </div>
    </div>

    <h2>🔧 手动下载方法</h2>

    <div class="info">
        <h3>如果自动下载失败，可以手动操作：</h3>
        <ol>
            <li>打开浏览器，访问：<a href="https://ckeditor.com/ckeditor-4/download/" target="_blank">https://ckeditor.com/ckeditor-4/download/</a></li>
            <li>选择 <strong>"Full Package"</strong> 版本</li>
            <li>点击 <strong>"Download CKEditor"</strong></li>
            <li>下载完成后，解压zip文件</li>
            <li>将解压后的所有文件复制到 <code>public/static/ckeditor4/</code> 目录</li>
        </ol>
    </div>

    <h2>📊 版本对比</h2>

    <div class="step">
        <h3>CKEditor 4 版本说明</h3>
        <ul>
            <li><strong>Basic</strong>：最基础版本，功能有限</li>
            <li><strong>Standard</strong>：标准版本，包含常用功能</li>
            <li><strong>Full</strong>：完整版本，包含所有插件（推荐）</li>
        </ul>
        
        <div class="warning">
            <p><strong>重要：</strong>必须下载 <strong>Full</strong> 版本才包含mention插件！</p>
        </div>
    </div>

    <h2>🚀 下载完成后的测试</h2>

    <div class="step">
        <h3>验证安装是否成功</h3>
        <ol>
            <li>确保文件已正确解压到 <code>public/static/ckeditor4/</code></li>
            <li>访问测试页面验证基本功能</li>
            <li>检查浏览器控制台是否有错误</li>
            <li>测试mention功能是否正常工作</li>
        </ol>
    </div>

    <h2>🔄 完整迁移流程</h2>

    <div class="success">
        <h3>迁移完成后的效果</h3>
        <ul>
            <li>✅ <strong>许可证问题解决</strong>：LGPL免费商业使用</li>
            <li>✅ <strong>所有功能保持</strong>：包括@用户提及</li>
            <li>✅ <strong>本地化部署</strong>：不依赖外部CDN</li>
            <li>✅ <strong>API完全兼容</strong>：现有代码无需修改</li>
            <li>✅ <strong>性能提升</strong>：本地加载更快</li>
        </ul>
    </div>

    <div class="info">
        <h3>💡 小贴士</h3>
        <ul>
            <li>下载时选择最新的稳定版本（当前推荐4.22.1）</li>
            <li>保留原有的CKEditor 5文件作为备份</li>
            <li>在测试环境先验证功能正常</li>
            <li>逐步替换生产环境的模板文件</li>
        </ul>
    </div>

    <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
        <h3>📞 需要帮助？</h3>
        <p>如果在下载或安装过程中遇到问题，请：</p>
        <ul style="text-align: left; display: inline-block;">
            <li>检查网络连接和防火墙设置</li>
            <li>尝试使用不同的下载链接</li>
            <li>确认目录权限设置正确</li>
            <li>查看浏览器控制台的错误信息</li>
        </ul>
    </div>
</body>
</html>
