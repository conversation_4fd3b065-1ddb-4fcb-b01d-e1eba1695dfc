/**
 * CKEditor 4 管理员版本配置
 * 从CKEditor 5完整迁移，保持所有功能不变
 * 许可证：LGPL（免费商业使用）
 */

// 从 ThinkPHP8 后端获取 Mention 用户
async function fetchMentionUsers(queryText) {
    try {
        const response = await fetch(`/admin/Basic/searchUsers?q=${encodeURIComponent(queryText)}`);
        const users = await response.json();

        return users.map(user => ({
            id: user.id,
            userId: user.id,
            name: user.name,
            fullName: user.name,
            link: `/iCommunity/user/${user.id}`
        }));
    } catch (error) {
        console.error('获取 Mention 用户失败:', error);
        return [];
    }
}

// CKEditor 4 管理员配置（对应原来的ckeditor5.js）
const adminEditorConfig = {
    language: 'zh-cn',
    height: 400,

    // 启用所有需要的插件（暂时移除mention，使用基础插件）
    extraPlugins: 'uploadimage,image2,colorbutton,colordialog,font,justify,tableresize,tabletools,specialchar,codesnippet,widget,lineutils,clipboard,notification',

    // 移除不需要的插件
    removePlugins: 'elementspath',

    // 工具栏配置 - 对应CKEditor 5的完整工具栏
    toolbar: [
        { name: 'document', items: ['Source', '-', 'NewPage', 'Preview', '-', 'Templates'] },
        { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
        { name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll', '-', 'SpellChecker', 'Scayt'] },
        '/',
        { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
        { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },
        { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
        '/',
        { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
        { name: 'colors', items: ['TextColor', 'BGColor'] },
        { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe'] },
        { name: 'tools', items: ['Maximize', 'ShowBlocks'] },
        { name: 'others', items: ['CodeSnippet'] }
    ],

    // 字体配置
    font_names: 'Arial/Arial, Helvetica, sans-serif;' +
        'Comic Sans MS/Comic Sans MS, cursive;' +
        'Courier New/Courier New, Courier, monospace;' +
        'Georgia/Georgia, serif;' +
        'Lucida Sans Unicode/Lucida Sans Unicode, Lucida Grande, sans-serif;' +
        'Tahoma/Tahoma, Geneva, sans-serif;' +
        'Times New Roman/Times New Roman, Times, serif;' +
        'Trebuchet MS/Trebuchet MS, Helvetica, sans-serif;' +
        'Verdana/Verdana, Geneva, sans-serif;' +
        '微软雅黑/Microsoft YaHei;' +
        '宋体/SimSun;' +
        '黑体/SimHei;' +
        '楷体/KaiTi',

    // 字体大小
    fontSize_sizes: '8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',

    // 颜色配置
    colorButton_colors: '000,800000,8B4513,2F4F4F,008080,000080,4B0082,696969,' +
        'B22222,A52A2A,DAA520,006400,40E0D0,0000CD,800080,808080,' +
        'F00,FF8C00,FFD700,008000,0FF,00F,EE82EE,A9A9A9,' +
        'FFA07A,FFA500,FFFF00,00FF00,AFEEEE,ADD8E6,DDA0DD,D3D3D3,' +
        'FFF0F5,FAEBD7,FFFFE0,F0FFF0,F0FFFF,F0F8FF,E6E6FA,FFF',

    // 图片上传配置
    filebrowserUploadUrl: '/admin/editorImage',
    filebrowserImageUploadUrl: '/admin/editorImage',

    // 图片上传方法
    uploadUrl: '/admin/editorImage',

    // 表格配置
    table_defaultCellPadding: 5,
    table_defaultCellSpacing: 0,

    // 代码片段配置
    codeSnippet_theme: 'default',
    codeSnippet_languages: {
        javascript: 'JavaScript',
        php: 'PHP',
        python: 'Python',
        java: 'Java',
        css: 'CSS',
        html: 'HTML',
        sql: 'SQL',
        xml: 'XML',
        json: 'JSON'
    },

    // Mention功能暂时禁用，等待完整版插件
    // mentions: [],

    // 内容过滤配置 - 允许mention相关的HTML
    allowedContent: true,
    extraAllowedContent: 'a[data-mention,data-user-id,href,class]; span[class,data-*]; div[class,data-*]',

    // 自动段落
    autoParagraph: false,

    // 回车键行为
    enterMode: CKEDITOR.ENTER_P,
    shiftEnterMode: CKEDITOR.ENTER_BR,

    // 移除空标签
    fillEmptyBlocks: false,

    // 样式配置
    stylesSet: [
        { name: '标题1', element: 'h1' },
        { name: '标题2', element: 'h2' },
        { name: '标题3', element: 'h3' },
        { name: '标题4', element: 'h4' },
        { name: '标题5', element: 'h5' },
        { name: '标题6', element: 'h6' },
        { name: '段落', element: 'p' },
        { name: '引用', element: 'blockquote' },
        { name: '代码', element: 'code' }
    ]
};

// CKEditor 4 管理器类 - 对应原来的CKEditorManager
class CKEditor4Manager {
    constructor() {
        this.editors = [];
    }

    async initAll(selector) {
        // 等待CKEditor加载完成
        if (typeof CKEDITOR === 'undefined') {
            console.error('CKEditor 4 未加载');
            return;
        }

        const elements = document.querySelectorAll(selector);

        for (const element of elements) {
            try {
                // 为每个元素创建唯一ID
                if (!element.id) {
                    element.id = 'ckeditor4_' + Math.random().toString(36).substring(2, 9);
                }

                const editor = CKEDITOR.replace(element.id, adminEditorConfig);

                // 编辑器准备就绪后的处理
                editor.on('instanceReady', (evt) => {
                    const editor = evt.editor;

                    // 设置最小高度
                    editor.ui.space('contents').setStyle('min-height', '300px');

                    // 查找字数统计容器
                    const container = element.closest('.editor-container');
                    const wordCountContainer = container ?
                        container.querySelector('.editor_container__word-count') : null;

                    if (wordCountContainer) {
                        // 创建字数统计显示
                        const wordCountDiv = document.createElement('div');
                        wordCountDiv.style.cssText = 'padding: 5px; font-size: 12px; color: #666; border-top: 1px solid #ddd;';
                        wordCountContainer.appendChild(wordCountDiv);

                        // 更新字数统计
                        const updateWordCount = () => {
                            const text = editor.getData().replace(/<[^>]*>/g, '');
                            const words = text.trim() ? text.trim().split(/\s+/).length : 0;
                            const chars = text.length;
                            wordCountDiv.textContent = `字数: ${words} | 字符: ${chars}`;
                        };

                        // 监听内容变化
                        editor.on('change', updateWordCount);
                        editor.on('key', updateWordCount);

                        // 初始更新
                        updateWordCount();
                    }
                });

                this.editors.push(editor);
            } catch (error) {
                console.error('Error initializing CKEditor 4:', error);
            }
        }
    }

    // 获取编辑器实例
    getEditor(index = 0) {
        return this.editors[index];
    }

    // 获取所有编辑器
    getAllEditors() {
        return this.editors;
    }

    // 销毁所有编辑器
    destroyAll() {
        this.editors.forEach(editor => {
            if (editor && editor.destroy) {
                editor.destroy();
            }
        });
        this.editors = [];
    }
}

// 获取提及的用户 - 对应原来的getMentionedUsers函数
function getMentionedUsers(editor) {
    const mentionedUsers = [];
    const content = editor.getData();

    // 使用正则表达式提取mention
    const regex = /<a[^>]*class="[^"]*mention[^"]*"[^>]*data-user-id="([^"]+)"[^>]*>@([^<]+)<\/a>/g;
    let match;

    while ((match = regex.exec(content)) !== null) {
        mentionedUsers.push({
            userId: match[1],
            userName: match[2]
        });
    }

    // 去重处理
    return [...new Map(mentionedUsers.map(item => [item.userId, item]))].map(([_, item]) => item);
}

// 全局可用
window.CKEditor4Manager = CKEditor4Manager;
window.CKEditorManager = CKEditor4Manager; // 保持兼容性
window.getMentionedUsers = getMentionedUsers;

console.log('CKEditor 4 管理员配置已加载');
