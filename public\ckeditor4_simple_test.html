<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor 4 简化版测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .editor-container {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .editor-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .info {
            background: #e3f2fd;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .buttons {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007cba; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1, h2 { color: #333; }
        .editor_container__word-count {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CKEditor 4 简化版测试</h1>

        <div class="success">
            <h3>✅ 基本功能已就绪</h3>
            <ul>
                <li><strong>许可证</strong>：LGPL（免费商业使用）</li>
                <li><strong>基本编辑</strong>：粗体、斜体、下划线等</li>
                <li><strong>列表和链接</strong>：有序列表、无序列表、超链接</li>
                <li><strong>图片上传</strong>：支持图片插入和上传</li>
                <li><strong>表格编辑</strong>：完整的表格功能</li>
                <li><strong>代码块</strong>：支持语法高亮</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ 暂未实现的功能</h3>
            <ul>
                <li><strong>@用户提及</strong>：需要额外的mention插件</li>
                <li><strong>高级插件</strong>：部分高级功能需要单独配置</li>
            </ul>
            <p><strong>下一步</strong>：我们将下载完整版CKEditor 4到本地，然后添加mention等高级功能。</p>
        </div>
    </div>

    <div class="container">
        <div class="editor-container">
            <div class="editor-header">🔧 管理员编辑器测试</div>
            <div class="info">
                <strong>测试功能：</strong>使用工具栏测试各种格式化功能，插入图片、表格等
            </div>
            <textarea id="admin-editor" class="tiny-editor">
                <h2>CKEditor 4 管理员版本测试</h2>
                <p>这是CKEditor 4的管理员版本，包含丰富的编辑功能：</p>
                <ul>
                    <li><strong>文本格式化</strong>：粗体、斜体、下划线、颜色等</li>
                    <li><strong>图片和媒体</strong>：支持图片上传和嵌入</li>
                    <li><strong>表格功能</strong>：完整的表格编辑工具</li>
                    <li><strong>代码块</strong>：支持语法高亮</li>
                </ul>
                <p>试试各种功能！</p>

                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <thead>
                        <tr>
                            <th>功能</th>
                            <th>状态</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>基本编辑</td>
                            <td>✅ 可用</td>
                            <td>粗体、斜体、下划线等</td>
                        </tr>
                        <tr>
                            <td>图片上传</td>
                            <td>✅ 可用</td>
                            <td>支持本地上传</td>
                        </tr>
                        <tr>
                            <td>@用户提及</td>
                            <td>⏳ 待实现</td>
                            <td>需要mention插件</td>
                        </tr>
                    </tbody>
                </table>
            </textarea>
            <div class="editor_container__word-count"></div>
        </div>
    </div>

    <div class="container">
        <div class="editor-container">
            <div class="editor-header">👥 前台编辑器测试</div>
            <div class="info">
                <strong>前台版本：</strong>功能简化，适合普通用户使用
            </div>
            <textarea id="home-editor" class="tiny-editor">
                <h2>CKEditor 4 前台版本测试</h2>
                <p>这是前台用户使用的简化版本：</p>
                <ul>
                    <li>基本的文本格式化</li>
                    <li>图片插入</li>
                    <li>简单的表格</li>
                </ul>
                <p>功能精简但足够日常使用！</p>
            </textarea>
            <div class="editor_container__word-count"></div>
        </div>
    </div>

    <div class="container">
        <div class="buttons">
            <button class="btn btn-success" onclick="getContent()">获取编辑器内容</button>
            <button class="btn btn-primary" onclick="setContent()">设置测试内容</button>
            <button class="btn btn-danger" onclick="clearContent()">清空内容</button>
        </div>

        <div class="info">
            <h3>📋 下一步计划</h3>
            <ol>
                <li><strong>下载完整版CKEditor 4</strong>：包含所有插件的完整版本</li>
                <li><strong>配置mention插件</strong>：实现@用户提及功能</li>
                <li><strong>完善配置</strong>：添加更多高级功能</li>
                <li><strong>完成迁移</strong>：替换所有模板中的引用</li>
            </ol>

            <h3>🔄 API兼容性</h3>
            <p><strong>好消息！</strong>现有的API调用方式完全不需要修改：</p>
            <ul>
                <li><code>CKEditorManager</code> 类保持不变</li>
                <li><code>initAll('.tiny-editor')</code> 方法保持不变</li>
                <li>HTML结构保持不变</li>
            </ul>
        </div>
    </div>

    <!-- 加载CKEditor 4 - 使用稳定的CDN -->
    <script src="https://cdn.ckeditor.com/4.26.1/standard-all/ckeditor.js"></script>

    <!-- 加载简化配置文件 -->
    <script src="/static/ckeditor4/ckeditor4-simple.js"></script>

    <script>
        // 等待CKEditor加载完成
        CKEDITOR.on('loaded', function() {
            console.log('CKEditor 4 已加载完成');

            // 初始化管理员编辑器
            const adminManager = new CKEditor4Manager();
            adminManager.initAll('#admin-editor');

            // 初始化前台编辑器
            setTimeout(() => {
                CKEDITOR.replace('home-editor', window.homeEditorConfig);
            }, 1000);
        });

        // 测试函数
        function getContent() {
            const adminEditor = CKEDITOR.instances['admin-editor'];
            const homeEditor = CKEDITOR.instances['home-editor'];

            if (adminEditor) {
                const content = adminEditor.getData();
                console.log('管理员编辑器内容:', content);
                alert('内容已输出到控制台，请按F12查看');
            }
        }

        function setContent() {
            const adminEditor = CKEDITOR.instances['admin-editor'];
            const homeEditor = CKEDITOR.instances['home-editor'];

            const testContent = `
                <h2>测试内容</h2>
                <p>这是通过JavaScript设置的测试内容，包含：</p>
                <ul>
                    <li><strong>粗体文本</strong></li>
                    <li><em>斜体文本</em></li>
                    <li><u>下划线文本</u></li>
                </ul>
                <p style="color: red;">彩色文本测试</p>
                <p>CKEditor 4 基本功能正常工作！</p>
            `;

            if (adminEditor) adminEditor.setData(testContent);
            if (homeEditor) homeEditor.setData('<h3>前台编辑器测试内容</h3><p>简化版功能正常！</p>');
        }

        function clearContent() {
            if (confirm('确定要清空所有编辑器内容吗？')) {
                const adminEditor = CKEDITOR.instances['admin-editor'];
                const homeEditor = CKEDITOR.instances['home-editor'];

                if (adminEditor) adminEditor.setData('');
                if (homeEditor) homeEditor.setData('');
            }
        }

        // 页面加载完成提示
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('CKEditor 4 简化版测试页面加载完成');
                console.log('可用的编辑器实例:', Object.keys(CKEDITOR.instances));
            }, 3000);
        });
    </script>
</body>
</html>
