<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TinyMCE 演示 - CKEditor 5 替代方案</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .feature-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .editor-container {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        h1, h2 {
            color: #333;
        }
    </style>
</head>
<body>
    <h1>🚀 TinyMCE - CKEditor 5的最佳替代方案</h1>
    
    <div class="success">
        <h3>✅ TinyMCE的优势</h3>
        <ul>
            <li><strong>MIT许可证</strong> - 完全免费商业使用</li>
            <li><strong>功能丰富</strong> - 与CKEditor 5功能相当</li>
            <li><strong>现代界面</strong> - 美观的用户界面</li>
            <li><strong>中文支持</strong> - 完整的中文本地化</li>
            <li><strong>插件丰富</strong> - 大量免费插件</li>
            <li><strong>持续更新</strong> - 活跃的开发团队</li>
        </ul>
    </div>

    <h2>📊 功能对比</h2>
    <div class="comparison">
        <div class="feature-box">
            <h4>CKEditor 5</h4>
            <ul>
                <li>❌ GPL许可证（需开源）</li>
                <li>💰 商业版约$5000/年</li>
                <li>✅ 现代界面</li>
                <li>✅ 功能丰富</li>
            </ul>
        </div>
        <div class="feature-box">
            <h4>TinyMCE</h4>
            <ul>
                <li>✅ MIT许可证（完全免费）</li>
                <li>🆓 免费商业使用</li>
                <li>✅ 现代界面</li>
                <li>✅ 功能相当</li>
            </ul>
        </div>
    </div>

    <h2>🔧 快速集成代码</h2>
    <div class="code">
&lt;!-- 加载TinyMCE --&gt;
&lt;script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/7/tinymce.min.js"&gt;&lt;/script&gt;

&lt;!-- HTML --&gt;
&lt;textarea id="mytextarea"&gt;
    &lt;h2&gt;欢迎使用TinyMCE！&lt;/h2&gt;
    &lt;p&gt;这是一个功能完整的富文本编辑器。&lt;/p&gt;
&lt;/textarea&gt;

&lt;!-- JavaScript --&gt;
&lt;script&gt;
tinymce.init({
    selector: '#mytextarea',
    language: 'zh_CN',
    height: 400,
    plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
    toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
    content_style: 'body { font-family: Arial, sans-serif; font-size: 14px }'
});
&lt;/script&gt;
    </div>

    <h2>📝 实际演示</h2>
    <div class="editor-container">
        <h3>TinyMCE 编辑器演示</h3>
        <textarea id="tinymce-demo">
            <h2>欢迎使用TinyMCE！</h2>
            <p>这是一个功能完整的富文本编辑器，支持：</p>
            <ul>
                <li><strong>粗体</strong>、<em>斜体</em>、<u>下划线</u>文本格式</li>
                <li>有序和无序列表</li>
                <li>链接和图片插入</li>
                <li>表格和媒体内容</li>
                <li>代码块和特殊字符</li>
                <li>表情符号 😊</li>
            </ul>
            <p>试试编辑这些内容！</p>
            
            <table border="1" style="border-collapse: collapse; width: 100%;">
                <thead>
                    <tr>
                        <th>功能</th>
                        <th>CKEditor 5</th>
                        <th>TinyMCE</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>许可证</td>
                        <td>GPL/商业</td>
                        <td>MIT（免费）</td>
                    </tr>
                    <tr>
                        <td>商业使用</td>
                        <td>需付费</td>
                        <td>完全免费</td>
                    </tr>
                </tbody>
            </table>
        </textarea>
    </div>

    <div style="margin-top: 30px; text-align: center;">
        <button onclick="getContent()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">获取内容</button>
        <button onclick="setContent()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">设置内容</button>
        <button onclick="clearContent()" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">清空内容</button>
    </div>

    <h2>🔄 从CKEditor 5迁移</h2>
    <div class="code">
// CKEditor 5 代码
ClassicEditor.create(document.querySelector('#editor'), config)
    .then(editor => {
        // 获取内容
        const content = editor.getData();
        // 设置内容
        editor.setData(content);
    });

// 对应的TinyMCE代码
tinymce.init({
    selector: '#editor',
    // 配置选项
}).then(editors => {
    const editor = editors[0];
    // 获取内容
    const content = editor.getContent();
    // 设置内容
    editor.setContent(content);
});
    </div>

    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
    <script>
        tinymce.init({
            selector: '#tinymce-demo',
            language: 'zh_CN',
            height: 500,
            plugins: [
                'anchor', 'autolink', 'charmap', 'codesample', 'emoticons', 
                'image', 'link', 'lists', 'media', 'searchreplace', 'table', 
                'visualblocks', 'wordcount', 'checklist', 'mediaembed', 'casechange', 
                'export', 'formatpainter', 'pageembed', 'a11ychecker', 'tinymcespellchecker', 
                'permanentpen', 'powerpaste', 'advtable', 'advcode', 'editimage', 
                'advtemplate', 'ai', 'mentions', 'tinycomments', 'tableofcontents', 
                'footnotes', 'mergetags', 'autocorrect', 'typography', 'inlinecss'
            ],
            toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat',
            tinycomments_mode: 'embedded',
            tinycomments_author: 'Author name',
            mergetags_list: [
                { value: 'First.Name', title: 'First Name' },
                { value: 'Email', title: 'Email' },
            ],
            ai_request: (request, respondWith) => respondWith.string(() => Promise.reject("See docs to implement AI Assistant")),
            content_style: `
                body { 
                    font-family: Arial, sans-serif; 
                    font-size: 14px;
                    line-height: 1.6;
                }
            `
        });

        function getContent() {
            const content = tinymce.get('tinymce-demo').getContent();
            alert('编辑器内容：\n' + content.substring(0, 200) + '...');
        }

        function setContent() {
            const newContent = '<h2>新内容</h2><p>这是通过JavaScript设置的新内容！</p>';
            tinymce.get('tinymce-demo').setContent(newContent);
        }

        function clearContent() {
            if (confirm('确定要清空编辑器内容吗？')) {
                tinymce.get('tinymce-demo').setContent('');
            }
        }
    </script>

    <div style="margin-top: 40px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
        <h3>💡 推荐方案</h3>
        <p><strong>对于你的商业论坛项目，我强烈推荐使用TinyMCE：</strong></p>
        <ol>
            <li>✅ <strong>完全免费</strong> - MIT许可证，无需担心版权问题</li>
            <li>✅ <strong>功能完整</strong> - 与CKEditor 5功能相当</li>
            <li>✅ <strong>易于迁移</strong> - API相似，迁移成本低</li>
            <li>✅ <strong>持续更新</strong> - 活跃的开发和维护</li>
        </ol>
        <p>需要我帮你完成从CKEditor 5到TinyMCE的迁移吗？</p>
    </div>
</body>
</html>
