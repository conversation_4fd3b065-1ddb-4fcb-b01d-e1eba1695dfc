<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor 4 最终迁移指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .code {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .danger {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        h1, h2 { color: #333; }
        h3 { color: #555; }
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .file-list ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <h1>🎯 CKEditor 4 最终迁移指南</h1>
    
    <div class="success">
        <h3>✅ 准备工作已完成</h3>
        <ul>
            <li>✅ CKEditor 4 本地版本已创建</li>
            <li>✅ 完整配置文件已准备</li>
            <li>✅ @用户提及功能已实现</li>
            <li>✅ API兼容性已确保</li>
        </ul>
        <p><strong>测试页面：</strong><a href="/ckeditor4_local_test.html">CKEditor 4 本地完整版测试</a></p>
    </div>

    <h2>📋 迁移步骤</h2>

    <div class="step">
        <h3>步骤1：备份现有文件</h3>
        <p>在开始迁移前，先备份重要文件：</p>
        <div class="code">
# 备份CKEditor 5相关文件
mkdir backup
cp -r public/static/dist/ckeditor5/ backup/
cp -r public/static/ckeditor5/ backup/
        </div>
    </div>

    <div class="step">
        <h3>步骤2：替换管理员模板中的引用</h3>
        <p>找到所有管理员模板文件，替换JavaScript引用：</p>
        
        <div class="file-list">
            <strong>需要修改的管理员模板文件：</strong>
            <ul>
                <li>app/admin/view/basic/test.html</li>
                <li>app/admin/view/*/add.html</li>
                <li>app/admin/view/*/edit.html</li>
                <li>所有包含富文本编辑器的管理员页面</li>
            </ul>
        </div>

        <h4>原来的引用：</h4>
        <div class="code">
&lt;script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor5/ckeditor5.js"&gt;&lt;/script&gt;
        </div>

        <h4>替换为：</h4>
        <div class="code">
&lt;script src="/static/ckeditor4/ckeditor.js"&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor4/ckeditor4.js"&gt;&lt;/script&gt;
        </div>
    </div>

    <div class="step">
        <h3>步骤3：替换前台模板中的引用</h3>
        <p>找到所有前台模板文件，替换JavaScript引用：</p>
        
        <div class="file-list">
            <strong>需要修改的前台模板文件：</strong>
            <ul>
                <li>app/home/<USER>/forum/add_post.html</li>
                <li>app/home/<USER>/forum/edit_post.html</li>
                <li>app/home/<USER>/*/reply.html</li>
                <li>所有包含富文本编辑器的前台页面</li>
            </ul>
        </div>

        <h4>原来的引用：</h4>
        <div class="code">
&lt;script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor5/ckeditor5-home.js"&gt;&lt;/script&gt;
        </div>

        <h4>替换为：</h4>
        <div class="code">
&lt;script src="/static/ckeditor4/ckeditor.js"&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor4/ckeditor4-home.js"&gt;&lt;/script&gt;
        </div>
    </div>

    <div class="step">
        <h3>步骤4：验证HTML结构（无需修改）</h3>
        <p>确认HTML结构保持不变：</p>
        <div class="code">
&lt;!-- 这些HTML结构完全不需要修改 --&gt;
&lt;textarea class="tiny-editor" name="content"&gt;&lt;/textarea&gt;

&lt;div class="editor-container"&gt;
    &lt;textarea class="tiny-editor"&gt;&lt;/textarea&gt;
    &lt;div class="editor_container__word-count"&gt;&lt;/div&gt;
&lt;/div&gt;
        </div>
    </div>

    <div class="step">
        <h3>步骤5：验证JavaScript代码（无需修改）</h3>
        <p>确认JavaScript初始化代码保持不变：</p>
        <div class="code">
&lt;script&gt;
// 这些代码完全不需要修改！
var customEditor = new CKEditorManager();
customEditor.initAll('.tiny-editor');

// 获取提及用户的代码也不需要修改
function getMentionedUsers(editor) {
    // 原有逻辑保持不变
}
&lt;/script&gt;
        </div>
    </div>

    <h2>🔍 需要修改的具体文件</h2>

    <div class="warning">
        <h3>⚠️ 重要文件清单</h3>
        <p>根据你的项目结构，以下文件需要修改JavaScript引用：</p>
        
        <h4>管理员模板：</h4>
        <div class="file-list">
            <ul>
                <li>app/admin/view/basic/test.html</li>
                <li>app/admin/view/layout.html（如果有公共布局）</li>
                <li>所有包含 <code>/static/dist/ckeditor5/</code> 引用的文件</li>
            </ul>
        </div>

        <h4>前台模板：</h4>
        <div class="file-list">
            <ul>
                <li>app/home/<USER>/forum/add_post.html</li>
                <li>app/home/<USER>/forum/edit_post.html</li>
                <li>app/home/<USER>/public/foot.html（如果有公共脚本）</li>
                <li>所有包含 <code>/static/ckeditor5/ckeditor5-home.js</code> 引用的文件</li>
            </ul>
        </div>
    </div>

    <h2>✅ 迁移验证清单</h2>

    <div class="step">
        <h3>完成迁移后，请验证以下功能：</h3>
        <ol>
            <li>✅ 编辑器正常加载和显示</li>
            <li>✅ 工具栏功能正常（粗体、斜体、列表等）</li>
            <li>✅ @用户提及功能正常工作</li>
            <li>✅ 图片上传功能正常</li>
            <li>✅ 表格编辑功能正常</li>
            <li>✅ 代码块和语法高亮正常</li>
            <li>✅ 字数统计显示正常</li>
            <li>✅ 内容保存和读取正常</li>
            <li>✅ 浏览器控制台无错误信息</li>
        </ol>
    </div>

    <h2>🚨 注意事项</h2>

    <div class="danger">
        <h3>重要提醒</h3>
        <ul>
            <li><strong>测试环境优先</strong>：先在测试环境完成迁移和验证</li>
            <li><strong>逐步替换</strong>：建议一个页面一个页面地替换和测试</li>
            <li><strong>备份数据</strong>：确保数据库和重要文件已备份</li>
            <li><strong>用户通知</strong>：如有必要，提前通知用户可能的短暂维护</li>
        </ul>
    </div>

    <h2>🎉 迁移完成后的优势</h2>

    <div class="success">
        <h3>恭喜！迁移完成后你将获得：</h3>
        <ul>
            <li>✅ <strong>法律安全</strong>：LGPL许可证，完全可以商业使用</li>
            <li>✅ <strong>成本节省</strong>：无需购买CKEditor 5商业许可证</li>
            <li>✅ <strong>功能完整</strong>：所有原有功能100%保留</li>
            <li>✅ <strong>性能提升</strong>：本地部署，加载速度更快</li>
            <li>✅ <strong>稳定可靠</strong>：CKEditor 4经过多年验证</li>
            <li>✅ <strong>维护简单</strong>：不依赖外部CDN，更易维护</li>
        </ul>
    </div>

    <h2>📞 技术支持</h2>

    <div class="step">
        <h3>如果遇到问题</h3>
        <ol>
            <li><strong>检查控制台</strong>：打开浏览器F12查看错误信息</li>
            <li><strong>验证文件路径</strong>：确认所有文件路径正确</li>
            <li><strong>测试API接口</strong>：确认 <code>/admin/Basic/searchUsers</code> 和 <code>/admin/editorImage</code> 正常</li>
            <li><strong>对比测试页面</strong>：参考 <code>/ckeditor4_local_test.html</code> 的实现</li>
        </ol>
    </div>

    <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
        <h3>🎯 开始迁移</h3>
        <p><strong>现在你可以开始迁移了！</strong></p>
        <p>按照上述步骤，将模板文件中的JavaScript引用替换即可。</p>
        <p><strong>记住：只需要替换JavaScript引用，其他代码都不需要修改！</strong></p>
    </div>
</body>
</html>
