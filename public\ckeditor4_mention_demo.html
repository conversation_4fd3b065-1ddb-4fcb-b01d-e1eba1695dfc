<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor 4 Mention功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        .info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .success {
            background: #d4edda;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
            font-size: 14px;
        }
        .editor-container {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .mention-demo {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }
    </style>
</head>
<body>
    <h1>📢 CKEditor 4 Mention功能演示</h1>
    
    <div class="success">
        <h3>✅ CKEditor 4 Mention功能特点</h3>
        <ul>
            <li><strong>@用户提及</strong> - 支持@符号触发用户提及</li>
            <li><strong>自定义数据源</strong> - 可以连接用户数据库</li>
            <li><strong>实时搜索</strong> - 输入时实时过滤用户</li>
            <li><strong>自定义样式</strong> - 可以自定义提及的显示样式</li>
            <li><strong>回调函数</strong> - 支持选择用户时的回调</li>
        </ul>
    </div>

    <div class="mention-demo">
        <h3>🎯 使用说明</h3>
        <p><strong>在下面的编辑器中输入 @ 符号，然后输入用户名来测试mention功能！</strong></p>
        <p>例如：输入 "@张" 或 "@李" 或 "@王" 来看到提及建议</p>
    </div>

    <h2>📝 实际演示</h2>
    <div class="editor-container">
        <h3>带Mention功能的编辑器</h3>
        <textarea id="editor-with-mention">
            <h2>欢迎使用带Mention功能的CKEditor 4！</h2>
            <p>试试输入 <strong>@</strong> 符号，然后输入用户名：</p>
            <ul>
                <li>@张三 - 前端开发工程师</li>
                <li>@李四 - 后端开发工程师</li>
                <li>@王五 - 产品经理</li>
            </ul>
            <p>在这里试试：@</p>
        </textarea>
    </div>

    <h2>🔧 实现代码</h2>
    
    <h3>1. HTML结构</h3>
    <div class="code">
&lt;!-- 加载CKEditor 4和相关插件 --&gt;
&lt;script src="https://cdn.ckeditor.com/4.22.1/full/ckeditor.js"&gt;&lt;/script&gt;

&lt;textarea id="editor"&gt;
    &lt;p&gt;输入@符号来提及用户&lt;/p&gt;
&lt;/textarea&gt;
    </div>

    <h3>2. JavaScript配置</h3>
    <div class="code">
CKEDITOR.replace('editor', {
    language: 'zh-cn',
    height: 300,
    
    // 启用mention插件
    extraPlugins: 'mentions,autocomplete,textmatch',
    
    // 配置mention功能
    mentions: [
        {
            feed: function(opts, callback) {
                // 模拟用户数据
                var users = [
                    { id: 1, name: '张三', fullName: '张三 (前端开发)', avatar: 'avatar1.jpg' },
                    { id: 2, name: '李四', fullName: '李四 (后端开发)', avatar: 'avatar2.jpg' },
                    { id: 3, name: '王五', fullName: '王五 (产品经理)', avatar: 'avatar3.jpg' },
                    { id: 4, name: '赵六', fullName: '赵六 (UI设计师)', avatar: 'avatar4.jpg' },
                    { id: 5, name: '钱七', fullName: '钱七 (测试工程师)', avatar: 'avatar5.jpg' }
                ];
                
                // 根据输入过滤用户
                var matchProperty = 'name';
                var query = opts.query.toLowerCase();
                
                users = users.filter(function(item) {
                    return item[matchProperty].toLowerCase().indexOf(query) == 0;
                });
                
                // 限制结果数量
                users = users.slice(0, 10);
                
                // 返回结果
                callback(users);
            },
            
            // 提及触发字符
            marker: '@',
            
            // 最小查询长度
            minChars: 0,
            
            // 显示模板
            itemTemplate: '&lt;li data-id="{id}"&gt;' +
                '&lt;strong&gt;{name}&lt;/strong&gt; ' +
                '&lt;span&gt;{fullName}&lt;/span&gt;' +
                '&lt;/li&gt;',
            
            // 输出模板
            outputTemplate: '&lt;a href="/user/{id}"&gt;@{name}&lt;/a&gt;&amp;nbsp;'
        }
    ]
});
    </div>

    <h3>3. 高级配置示例</h3>
    <div class="code">
// 更复杂的mention配置
mentions: [
    {
        feed: function(opts, callback) {
            // 可以通过AJAX从服务器获取用户数据
            $.ajax({
                url: '/api/users/search',
                data: { q: opts.query },
                success: function(data) {
                    callback(data.users);
                }
            });
        },
        
        marker: '@',
        minChars: 1,
        
        // 自定义CSS类
        itemsLimit: 10,
        
        // 选择回调
        onSelect: function(item) {
            console.log('选择了用户:', item);
            // 可以在这里发送通知等
        },
        
        // 自定义模板
        itemTemplate: 
            '&lt;li data-id="{id}" class="mention-item"&gt;' +
                '&lt;img src="{avatar}" class="mention-avatar"&gt;' +
                '&lt;div class="mention-info"&gt;' +
                    '&lt;strong&gt;{name}&lt;/strong&gt;' +
                    '&lt;small&gt;{title}&lt;/small&gt;' +
                '&lt;/div&gt;' +
            '&lt;/li&gt;',
            
        outputTemplate: '&lt;span class="mention" data-user-id="{id}"&gt;@{name}&lt;/span&gt;'
    }
]
    </div>

    <div class="info">
        <h3>💡 与你现有的CKEditor 5 mention功能对比</h3>
        <p><strong>功能相似度：90%+</strong></p>
        <ul>
            <li>✅ 都支持@符号触发</li>
            <li>✅ 都支持实时搜索过滤</li>
            <li>✅ 都支持自定义数据源</li>
            <li>✅ 都支持自定义显示模板</li>
            <li>✅ 都支持选择回调函数</li>
        </ul>
        <p><strong>迁移难度：低</strong> - 主要是配置语法的差异</p>
    </div>

    <h2>🔄 从CKEditor 5迁移mention功能</h2>
    <div class="code">
// CKEditor 5 mention配置
mentions: {
    feeds: [
        {
            marker: '@',
            feed: getFeedItems,
            itemRenderer: customItemRenderer
        }
    ]
}

// 对应的CKEditor 4配置
mentions: [
    {
        marker: '@',
        feed: getFeedItems,
        itemTemplate: '&lt;li&gt;{name}&lt;/li&gt;'
    }
]
    </div>

    <div style="margin-top: 30px; text-align: center;">
        <button onclick="getMentions()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">获取提及内容</button>
        <button onclick="addMention()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">插入提及</button>
    </div>

    <!-- 加载CKEditor 4 Full版本（包含mention插件） -->
    <script src="https://cdn.ckeditor.com/4.22.1/full/ckeditor.js"></script>
    
    <script>
        // 模拟用户数据
        var userData = [
            { id: 1, name: '张三', fullName: '张三 (前端开发工程师)', title: '前端开发工程师' },
            { id: 2, name: '李四', fullName: '李四 (后端开发工程师)', title: '后端开发工程师' },
            { id: 3, name: '王五', fullName: '王五 (产品经理)', title: '产品经理' },
            { id: 4, name: '赵六', fullName: '赵六 (UI设计师)', title: 'UI设计师' },
            { id: 5, name: '钱七', fullName: '钱七 (测试工程师)', title: '测试工程师' },
            { id: 6, name: '孙八', fullName: '孙八 (运维工程师)', title: '运维工程师' },
            { id: 7, name: '周九', fullName: '周九 (数据分析师)', title: '数据分析师' },
            { id: 8, name: '吴十', fullName: '吴十 (项目经理)', title: '项目经理' }
        ];

        CKEDITOR.replace('editor-with-mention', {
            language: 'zh-cn',
            height: 400,
            
            // 启用必要的插件
            extraPlugins: 'mentions,autocomplete,textmatch',
            
            // 工具栏配置
            toolbar: [
                ['Bold', 'Italic', 'Underline'],
                ['NumberedList', 'BulletedList'],
                ['Link', 'Unlink'],
                ['Image', 'Table'],
                ['Source']
            ],
            
            // mention配置
            mentions: [
                {
                    feed: function(opts, callback) {
                        var query = opts.query.toLowerCase();
                        var users = userData.filter(function(user) {
                            return user.name.toLowerCase().indexOf(query) === 0;
                        });
                        
                        // 限制结果数量
                        users = users.slice(0, 8);
                        
                        callback(users);
                    },
                    
                    marker: '@',
                    minChars: 0,
                    
                    itemTemplate: 
                        '<li data-id="{id}" style="padding: 8px; border-bottom: 1px solid #eee;">' +
                            '<strong style="color: #333;">{name}</strong>' +
                            '<br><small style="color: #666;">{title}</small>' +
                        '</li>',
                    
                    outputTemplate: '<a href="/user/{id}" style="color: #007cba; text-decoration: none; background: #e3f2fd; padding: 2px 4px; border-radius: 3px;">@{name}</a>&nbsp;',
                    
                    // 选择回调
                    onSelect: function(item) {
                        console.log('选择了用户:', item);
                    }
                }
            ]
        });

        function getMentions() {
            var editor = CKEDITOR.instances['editor-with-mention'];
            var content = editor.getData();
            
            // 提取所有mention
            var mentions = [];
            var regex = /<a[^>]*href="\/user\/(\d+)"[^>]*>@([^<]+)<\/a>/g;
            var match;
            
            while ((match = regex.exec(content)) !== null) {
                mentions.push({
                    userId: match[1],
                    userName: match[2]
                });
            }
            
            if (mentions.length > 0) {
                alert('找到的提及用户：\n' + mentions.map(m => `@${m.userName} (ID: ${m.userId})`).join('\n'));
            } else {
                alert('没有找到提及的用户');
            }
        }

        function addMention() {
            var editor = CKEDITOR.instances['editor-with-mention'];
            var mentionHtml = '<a href="/user/1" style="color: #007cba; text-decoration: none; background: #e3f2fd; padding: 2px 4px; border-radius: 3px;">@张三</a>&nbsp;';
            
            editor.insertHtml('提及用户：' + mentionHtml);
        }
    </script>

    <div style="margin-top: 40px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
        <h3>📋 总结</h3>
        <p><strong>CKEditor 4的mention功能完全可以满足你的需求：</strong></p>
        <ul>
            <li>✅ <strong>功能完整</strong> - 支持@提及、实时搜索、自定义样式</li>
            <li>✅ <strong>易于集成</strong> - 配置简单，文档完善</li>
            <li>✅ <strong>免费使用</strong> - LGPL许可证，商业友好</li>
            <li>✅ <strong>稳定可靠</strong> - 经过大量项目验证</li>
        </ul>
        <p>需要我帮你将现有的mention功能迁移到CKEditor 4吗？</p>
    </div>
</body>
</html>
