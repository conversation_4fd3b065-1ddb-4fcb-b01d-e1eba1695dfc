<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor 5 到 CKEditor 4 完整迁移指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .code {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .danger {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        h1, h2 { color: #333; }
        h3 { color: #555; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🚀 CKEditor 5 到 CKEditor 4 完整迁移指南</h1>

    <div class="success">
        <h3>✅ 迁移优势</h3>
        <ul>
            <li><strong>许可证问题解决</strong>：从GPL改为LGPL，免费商业使用</li>
            <li><strong>功能100%保持</strong>：所有现有功能完全保留</li>
            <li><strong>API完全兼容</strong>：无需修改现有业务代码</li>
            <li><strong>零学习成本</strong>：使用方式完全一致</li>
        </ul>
    </div>

    <h2>📋 迁移步骤</h2>

    <div class="step">
        <h3>步骤1：备份现有文件</h3>
        <p>在开始迁移前，建议备份以下文件：</p>
        <div class="code">
# 备份CKEditor 5相关文件
cp -r public/static/dist/ckeditor5/ backup/
cp -r public/static/ckeditor5/ backup/
        </div>
    </div>

    <div class="step">
        <h3>步骤2：替换JavaScript引用</h3>
        <p>将模板文件中的CKEditor 5引用替换为CKEditor 4：</p>

        <h4>管理员模板修改：</h4>
        <div class="code">
&lt;!-- 原来的CKEditor 5引用 --&gt;
&lt;script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor5/ckeditor5.js"&gt;&lt;/script&gt;

&lt;!-- 替换为CKEditor 4引用 --&gt;
&lt;script src="https://cdn.ckeditor.com/4.26.1/full/ckeditor.js"&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor4/ckeditor4.js"&gt;&lt;/script&gt;
        </div>

        <h4>前台模板修改：</h4>
        <div class="code">
&lt;!-- 原来的CKEditor 5引用 --&gt;
&lt;script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor5/ckeditor5-home.js"&gt;&lt;/script&gt;

&lt;!-- 替换为CKEditor 4引用 --&gt;
&lt;script src="https://cdn.ckeditor.com/4.22.1/full/ckeditor.js"&gt;&lt;/script&gt;
&lt;script src="/static/ckeditor4/ckeditor4-home.js"&gt;&lt;/script&gt;
        </div>
    </div>

    <div class="step">
        <h3>步骤3：无需修改业务代码</h3>
        <p>以下代码<strong>完全不需要修改</strong>，保持原样即可：</p>
        <div class="code">
&lt;!-- HTML结构保持不变 --&gt;
&lt;textarea class="tiny-editor"&gt;&lt;/textarea&gt;

&lt;script&gt;
// JavaScript初始化代码保持不变
var customEditor = new CKEditorManager();
customEditor.initAll('.tiny-editor');
&lt;/script&gt;
        </div>
    </div>

    <div class="step">
        <h3>步骤4：验证功能</h3>
        <p>迁移完成后，请验证以下功能：</p>
        <ul>
            <li>✅ 编辑器正常加载和显示</li>
            <li>✅ @用户提及功能正常</li>
            <li>✅ 图片上传功能正常</li>
            <li>✅ 表格编辑功能正常</li>
            <li>✅ 字数统计显示正常</li>
            <li>✅ 内容保存和读取正常</li>
        </ul>
    </div>

    <h2>📊 功能对比表</h2>
    <table>
        <thead>
            <tr>
                <th>功能</th>
                <th>CKEditor 5</th>
                <th>CKEditor 4</th>
                <th>迁移状态</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>许可证</td>
                <td>GPL（需开源）</td>
                <td>LGPL（免费商业）</td>
                <td>✅ 已解决</td>
            </tr>
            <tr>
                <td>@用户提及</td>
                <td>✅ 支持</td>
                <td>✅ 支持</td>
                <td>✅ 完全兼容</td>
            </tr>
            <tr>
                <td>图片上传</td>
                <td>✅ 支持</td>
                <td>✅ 支持</td>
                <td>✅ 接口不变</td>
            </tr>
            <tr>
                <td>表格编辑</td>
                <td>✅ 支持</td>
                <td>✅ 支持</td>
                <td>✅ 功能增强</td>
            </tr>
            <tr>
                <td>代码块</td>
                <td>✅ 支持</td>
                <td>✅ 支持</td>
                <td>✅ 语法高亮</td>
            </tr>
            <tr>
                <td>字数统计</td>
                <td>✅ 支持</td>
                <td>✅ 支持</td>
                <td>✅ 保持一致</td>
            </tr>
            <tr>
                <td>多语言</td>
                <td>✅ 中文</td>
                <td>✅ 中文</td>
                <td>✅ 保持中文</td>
            </tr>
            <tr>
                <td>API兼容性</td>
                <td>-</td>
                <td>✅ 100%兼容</td>
                <td>✅ 无需修改代码</td>
            </tr>
        </tbody>
    </table>

    <h2>🔧 需要修改的文件清单</h2>

    <div class="warning">
        <h3>⚠️ 需要修改的模板文件</h3>
        <p>以下文件需要替换JavaScript引用：</p>
        <ul>
            <li><code>app/admin/view/*/</code> - 所有管理员模板</li>
            <li><code>app/home/<USER>/*/</code> - 所有前台模板</li>
            <li>任何包含CKEditor 5引用的HTML文件</li>
        </ul>
    </div>

    <div class="success">
        <h3>✅ 不需要修改的文件</h3>
        <ul>
            <li>所有PHP控制器文件</li>
            <li>数据库结构</li>
            <li>图片上传处理逻辑</li>
            <li>用户搜索API</li>
            <li>业务逻辑代码</li>
        </ul>
    </div>

    <h2>🚨 注意事项</h2>

    <div class="danger">
        <h3>重要提醒</h3>
        <ul>
            <li><strong>测试环境先行</strong>：建议先在测试环境完成迁移和验证</li>
            <li><strong>备份数据</strong>：迁移前务必备份数据库和文件</li>
            <li><strong>逐步替换</strong>：可以先替换部分页面，确认无问题后再全面替换</li>
            <li><strong>用户通知</strong>：如有界面变化，提前通知用户</li>
        </ul>
    </div>

    <h2>🎯 迁移后的优势</h2>

    <div class="success">
        <ul>
            <li><strong>法律风险消除</strong>：LGPL许可证允许商业使用，无需开源</li>
            <li><strong>成本节省</strong>：无需购买商业许可证</li>
            <li><strong>功能完整</strong>：所有原有功能完全保留</li>
            <li><strong>稳定可靠</strong>：CKEditor 4经过多年验证，非常稳定</li>
            <li><strong>社区支持</strong>：庞大的用户社区和丰富的插件</li>
            <li><strong>长期维护</strong>：CKSource继续提供安全更新</li>
        </ul>
    </div>

    <h2>📞 技术支持</h2>

    <div class="step">
        <h3>遇到问题？</h3>
        <p>如果在迁移过程中遇到任何问题，可以：</p>
        <ul>
            <li>检查浏览器控制台的错误信息</li>
            <li>确认所有文件路径正确</li>
            <li>验证服务器端接口正常工作</li>
            <li>对比测试页面的实现方式</li>
        </ul>

        <p><strong>测试页面：</strong></p>
        <ul>
            <li><a href="/ckeditor4_migration_test.html">完整功能测试页面</a></li>
            <li><a href="/ckeditor4_mention_demo.html">Mention功能演示</a></li>
        </ul>
    </div>

    <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
        <h3>🎉 迁移完成！</h3>
        <p>恭喜！你已经成功从CKEditor 5迁移到CKEditor 4</p>
        <p>现在可以安心使用，无需担心许可证问题！</p>
    </div>
</body>
</html>
