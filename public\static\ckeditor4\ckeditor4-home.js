/**
 * CKEditor 4 前台版本配置
 * 从CKEditor 5前台版本迁移，保持简化功能
 * 许可证：LGPL（免费商业使用）
 */

// 从 ThinkPHP8 后端获取 Mention 用户（复用管理员版本的函数）
async function fetchMentionUsers(queryText) {
    try {
        const response = await fetch(`/admin/Basic/searchUsers?q=${encodeURIComponent(queryText)}`);
        const users = await response.json();

        return users.map(user => ({
            id: user.id,
            userId: user.id,
            name: user.name,
            fullName: user.name,
            link: `/iCommunity/user/${user.id}`
        }));
    } catch (error) {
        console.error('获取 Mention 用户失败:', error);
        return [];
    }
}

// CKEditor 4 前台配置（对应原来的ckeditor5-home.js）
const homeEditorConfig = {
    language: 'zh-cn',
    height: 300,

    // 启用前台需要的插件（暂时移除mention，使用基础插件）
    extraPlugins: 'uploadimage,image2,colorbutton,font,specialchar,widget,lineutils,clipboard,notification',

    // 移除不需要的插件
    removePlugins: 'elementspath',

    // 简化的工具栏配置 - 对应CKEditor 5前台版本
    toolbar: [
        { name: 'clipboard', items: ['Undo', 'Redo'] },
        { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
        { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'] },
        { name: 'links', items: ['Link', 'Unlink'] },
        { name: 'insert', items: ['Image', 'Table', 'Smiley', 'SpecialChar'] },
        { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
        { name: 'colors', items: ['TextColor', 'BGColor'] },
        { name: 'paragraph2', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight'] }
    ],

    // 字体配置（简化版）
    font_names: 'Arial/Arial, Helvetica, sans-serif;' +
        'Georgia/Georgia, serif;' +
        'Times New Roman/Times New Roman, Times, serif;' +
        'Verdana/Verdana, Geneva, sans-serif;' +
        '微软雅黑/Microsoft YaHei;' +
        '宋体/SimSun;' +
        '黑体/SimHei',

    // 字体大小（简化版）
    fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;24/24px;28/28px;36/36px',

    // 颜色配置（简化版）
    colorButton_colors: '000,800000,8B4513,2F4F4F,008080,000080,4B0082,696969,' +
        'B22222,A52A2A,DAA520,006400,40E0D0,0000CD,800080,808080,' +
        'F00,FF8C00,FFD700,008000,0FF,00F,EE82EE,A9A9A9,' +
        'FFA07A,FFA500,FFFF00,00FF00,AFEEEE,ADD8E6,DDA0DD,D3D3D3',

    // 图片上传配置
    filebrowserUploadUrl: '/admin/editorImage',
    filebrowserImageUploadUrl: '/admin/editorImage',
    uploadUrl: '/admin/editorImage',

    // 表格配置
    table_defaultCellPadding: 5,
    table_defaultCellSpacing: 0,

    // Mention功能暂时禁用，等待完整版插件
    // mentions: [],

    // 内容过滤配置
    allowedContent: true,
    extraAllowedContent: 'a[data-mention,data-user-id,href,class]; span[class,data-*]; div[class,data-*]',

    // 自动段落
    autoParagraph: false,

    // 回车键行为
    enterMode: CKEDITOR.ENTER_P,
    shiftEnterMode: CKEDITOR.ENTER_BR,

    // 移除空标签
    fillEmptyBlocks: false,

    // 样式配置（简化版）
    stylesSet: [
        { name: '标题1', element: 'h1' },
        { name: '标题2', element: 'h2' },
        { name: '标题3', element: 'h3' },
        { name: '段落', element: 'p' },
        { name: '引用', element: 'blockquote' }
    ]
};

// CKEditor 4 前台管理器类
class CKEditor4HomeManager {
    constructor() {
        this.editors = [];
    }

    async initAll(selector) {
        // 等待CKEditor加载完成
        if (typeof CKEDITOR === 'undefined') {
            console.error('CKEditor 4 未加载');
            return;
        }

        const elements = document.querySelectorAll(selector);

        for (const element of elements) {
            try {
                // 为每个元素创建唯一ID
                if (!element.id) {
                    element.id = 'ckeditor4_home_' + Math.random().toString(36).substring(2, 9);
                }

                const editor = CKEDITOR.replace(element.id, homeEditorConfig);

                // 编辑器准备就绪后的处理
                editor.on('instanceReady', (evt) => {
                    const editor = evt.editor;

                    // 设置最小高度
                    editor.ui.space('contents').setStyle('min-height', '300px');

                    // 查找字数统计容器
                    const container = element.closest('.editor-container');
                    const wordCountContainer = container ?
                        container.querySelector('.editor_container__word-count') : null;

                    if (wordCountContainer) {
                        // 创建字数统计显示
                        const wordCountDiv = document.createElement('div');
                        wordCountDiv.style.cssText = 'padding: 5px; font-size: 12px; color: #666; border-top: 1px solid #ddd;';
                        wordCountContainer.appendChild(wordCountDiv);

                        // 更新字数统计
                        const updateWordCount = () => {
                            const text = editor.getData().replace(/<[^>]*>/g, '');
                            const words = text.trim() ? text.trim().split(/\s+/).length : 0;
                            const chars = text.length;
                            wordCountDiv.textContent = `字数: ${words} | 字符: ${chars}`;
                        };

                        // 监听内容变化
                        editor.on('change', updateWordCount);
                        editor.on('key', updateWordCount);

                        // 初始更新
                        updateWordCount();
                    }
                });

                this.editors.push(editor);
            } catch (error) {
                console.error('Error initializing CKEditor 4 Home:', error);
            }
        }
    }

    // 获取编辑器实例
    getEditor(index = 0) {
        return this.editors[index];
    }

    // 获取所有编辑器
    getAllEditors() {
        return this.editors;
    }

    // 销毁所有编辑器
    destroyAll() {
        this.editors.forEach(editor => {
            if (editor && editor.destroy) {
                editor.destroy();
            }
        });
        this.editors = [];
    }
}

// 获取提及的用户 - 前台版本
function getMentionedUsers(editor) {
    const mentionedUsers = [];
    const content = editor.getData();

    // 使用正则表达式提取mention
    const regex = /<a[^>]*class="[^"]*mention[^"]*"[^>]*data-user-id="([^"]+)"[^>]*>@([^<]+)<\/a>/g;
    let match;

    while ((match = regex.exec(content)) !== null) {
        mentionedUsers.push({
            userId: match[1],
            userName: match[2]
        });
    }

    // 去重处理
    return [...new Map(mentionedUsers.map(item => [item.userId, item]))].map(([_, item]) => item);
}

// 全局可用 - 保持与原来的兼容性
window.CKEditor4HomeManager = CKEditor4HomeManager;
window.CKEditorManager = CKEditor4HomeManager; // 保持兼容性
window.getMentionedUsers = getMentionedUsers;

console.log('CKEditor 4 前台配置已加载');
