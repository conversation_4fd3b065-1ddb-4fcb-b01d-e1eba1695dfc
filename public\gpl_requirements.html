<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPL许可证要求说明</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }
        .danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        h1, h2 {
            color: #333;
        }
        h3 {
            color: #555;
        }
        ul li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <h1>🚨 GPL许可证要求详解</h1>
    
    <div class="danger">
        <h3>⚠️ 重要警告</h3>
        <p><strong>仅仅将代码放在阿里云私有仓库是不够的！</strong></p>
        <p>GPL要求你必须向所有用户公开提供源代码访问权限。</p>
    </div>

    <h2>📋 GPL许可证的具体要求</h2>

    <h3>1. 源代码公开要求</h3>
    <ul>
        <li>✅ <strong>必须做</strong>：在网站上提供源代码下载链接</li>
        <li>✅ <strong>必须做</strong>：使用公开的Git仓库（如GitHub、GitLab）</li>
        <li>✅ <strong>必须做</strong>：包含完整的构建和部署说明</li>
        <li>❌ <strong>不够</strong>：仅放在私有的阿里云仓库</li>
        <li>❌ <strong>不够</strong>：只提供部分代码</li>
    </ul>

    <h3>2. 许可证声明要求</h3>
    <div class="code">
网站页脚必须包含：
"本网站使用GPL许可证开源软件，源代码可在 [GitHub链接] 获取"
</div>

    <h3>3. 文件头部声明</h3>
    <div class="code">
每个源代码文件都应包含：
/*
 * Copyright (C) 2024 OpenDelClub
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 */
</div>

    <h3>4. 必须提供的文件</h3>
    <ul>
        <li><strong>LICENSE</strong> - GPL许可证全文</li>
        <li><strong>README.md</strong> - 项目说明和安装指南</li>
        <li><strong>INSTALL</strong> - 详细的安装和部署说明</li>
        <li><strong>所有源代码</strong> - 包括配置文件、数据库结构等</li>
    </ul>

    <div class="warning">
        <h3>🤔 对商业项目的影响</h3>
        <ul>
            <li><strong>竞争对手</strong>可以获取你的全部源代码</li>
            <li><strong>用户</strong>可以自己搭建相同的网站</li>
            <li><strong>商业机密</strong>无法保护</li>
            <li><strong>盈利模式</strong>可能受到影响</li>
        </ul>
    </div>

    <h2>💡 建议的解决方案</h2>

    <div class="success">
        <h3>方案1：购买商业许可证（推荐）</h3>
        <ul>
            <li>✅ 保护源代码不公开</li>
            <li>✅ 获得技术支持</li>
            <li>✅ 无GPL限制</li>
            <li>✅ 适合商业项目</li>
        </ul>
        <p><strong>CKEditor 5商业许可证</strong>：约 $4,990/年（具体价格请咨询官方）</p>
    </div>

    <div class="success">
        <h3>方案2：使用替代编辑器</h3>
        <ul>
            <li><strong>TinyMCE</strong> - MIT许可证，完全免费</li>
            <li><strong>Quill.js</strong> - BSD许可证，轻量级</li>
            <li><strong>Froala Editor</strong> - 有免费版本</li>
            <li><strong>Summernote</strong> - MIT许可证</li>
        </ul>
    </div>

    <h2>🔍 如果坚持使用GPL版本</h2>

    <h3>必须完成的步骤：</h3>
    <ol>
        <li><strong>创建公开仓库</strong>
            <div class="code">
1. 在GitHub创建公开仓库
2. 上传完整项目代码
3. 包含所有配置文件和数据库结构
            </div>
        </li>
        
        <li><strong>添加许可证文件</strong>
            <div class="code">
1. 下载GPL v3许可证文本
2. 保存为项目根目录的LICENSE文件
3. 在每个源文件添加版权声明
            </div>
        </li>
        
        <li><strong>网站声明</strong>
            <div class="code">
在网站页脚添加：
"本站基于GPL许可证开源，源代码：https://github.com/你的用户名/opendelclub"
            </div>
        </li>
        
        <li><strong>提供下载</strong>
            <div class="code">
在网站添加"源代码"页面，提供：
- GitHub仓库链接
- 源代码ZIP下载
- 安装部署说明
            </div>
        </li>
    </ol>

    <div class="danger">
        <h3>⚠️ 法律风险提醒</h3>
        <p>如果使用GPL软件但不遵守许可证要求，可能面临：</p>
        <ul>
            <li>版权侵权诉讼</li>
            <li>强制开源要求</li>
            <li>经济赔偿</li>
            <li>声誉损失</li>
        </ul>
    </div>

    <h2>🎯 我的建议</h2>
    
    <p><strong>考虑到你的项目是对外开放的商业论坛，我强烈建议：</strong></p>
    
    <ol>
        <li><strong>优先选择</strong>：购买CKEditor 5商业许可证</li>
        <li><strong>经济选择</strong>：切换到TinyMCE（MIT许可证，功能相似）</li>
        <li><strong>不建议</strong>：使用GPL版本（除非你真的愿意完全开源）</li>
    </ol>

    <div class="warning">
        <h3>💭 思考问题</h3>
        <ul>
            <li>你愿意将整个网站代码公开吗？</li>
            <li>你的商业模式能承受完全开源吗？</li>
            <li>你有时间和精力维护开源项目吗？</li>
        </ul>
        <p>如果答案是"否"，那么GPL版本不适合你的项目。</p>
    </div>

    <p style="text-align: center; margin-top: 40px; color: #666;">
        <em>需要帮助选择和实施解决方案吗？我可以协助你完成迁移。</em>
    </p>
</body>
</html>
