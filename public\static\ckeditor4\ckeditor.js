/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
(function(){
    if(window.CKEDITOR && window.CKEDITOR.dom) return;

    window.CKEDITOR_BASEPATH = (function(){
        var scripts = document.getElementsByTagName('script');
        for(var i = 0; i < scripts.length; i++){
            var src = scripts[i].src;
            if(src && src.indexOf('ckeditor.js') !== -1){
                return src.substring(0, src.lastIndexOf('/') + 1);
            }
        }
        return '/static/ckeditor4/';
    })();

    // 动态加载CKEditor 4从CDN，但设置本地路径
    var script = document.createElement('script');
    script.src = 'https://cdn.ckeditor.com/4.26.1/full/ckeditor.js';
    script.onload = function() {
        // 设置本地基础路径
        if(window.CKEDITOR) {
            CKEDITOR.basePath = window.CKEDITOR_BASEPATH;

            // 添加中文语言包
            CKEDITOR.lang.load('zh-cn', 'zh-cn', function() {
                console.log('CKEditor 4 中文语言包已加载');
            });

            // 触发加载完成事件
            if(CKEDITOR.status === 'loaded') {
                CKEDITOR.fire('loaded');
            }
        }
    };
    script.onerror = function() {
        console.error('CKEditor 4 加载失败，请检查网络连接');
    };
    document.head.appendChild(script);
})();
