/**
 * CKEditor 4 简化版配置
 * 先实现基本功能，后续再添加mention等高级功能
 * 许可证：LGPL（免费商业使用）
 */

// CKEditor 4 管理员配置（简化版）
const adminEditorConfig = {
    language: 'zh-cn',
    height: 400,
    
    // 启用基本插件
    extraPlugins: 'uploadimage,image2,colorbutton,colordialog,font,justify,tableresize,tabletools,specialchar,codesnippet',
    
    // 移除不需要的插件
    removePlugins: 'elementspath',
    
    // 工具栏配置
    toolbar: [
        { name: 'document', items: ['Source', '-', 'NewPage', 'Preview'] },
        { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
        '/',
        { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', '-', 'RemoveFormat'] },
        { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote'] },
        { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
        '/',
        { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
        { name: 'colors', items: ['TextColor', 'BGColor'] },
        { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar'] },
        { name: 'tools', items: ['Maximize', 'ShowBlocks'] },
        { name: 'others', items: ['CodeSnippet'] }
    ],
    
    // 字体配置
    font_names: 'Arial/Arial, Helvetica, sans-serif;' +
        'Times New Roman/Times New Roman, Times, serif;' +
        'Verdana/Verdana, Geneva, sans-serif;' +
        '微软雅黑/Microsoft YaHei;' +
        '宋体/SimSun;' +
        '黑体/SimHei',
    
    // 字体大小
    fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;24/24px;28/28px;36/36px',
    
    // 图片上传配置
    filebrowserUploadUrl: '/admin/editorImage',
    filebrowserImageUploadUrl: '/admin/editorImage',
    uploadUrl: '/admin/editorImage',
    
    // 表格配置
    table_defaultCellPadding: 5,
    table_defaultCellSpacing: 0,
    
    // 代码片段配置
    codeSnippet_theme: 'default',
    codeSnippet_languages: {
        javascript: 'JavaScript',
        php: 'PHP',
        python: 'Python',
        java: 'Java',
        css: 'CSS',
        html: 'HTML',
        sql: 'SQL'
    },
    
    // 内容过滤配置
    allowedContent: true,
    
    // 自动段落
    autoParagraph: false,
    
    // 回车键行为
    enterMode: CKEDITOR.ENTER_P,
    shiftEnterMode: CKEDITOR.ENTER_BR
};

// CKEditor 4 前台配置（简化版）
const homeEditorConfig = {
    language: 'zh-cn',
    height: 300,
    
    // 启用基本插件
    extraPlugins: 'uploadimage,image2,colorbutton,font,specialchar',
    
    // 移除不需要的插件
    removePlugins: 'elementspath',
    
    // 简化的工具栏配置
    toolbar: [
        { name: 'clipboard', items: ['Undo', 'Redo'] },
        { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
        { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'] },
        { name: 'links', items: ['Link', 'Unlink'] },
        { name: 'insert', items: ['Image', 'Table', 'SpecialChar'] },
        { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
        { name: 'colors', items: ['TextColor', 'BGColor'] }
    ],
    
    // 字体配置（简化版）
    font_names: 'Arial/Arial, Helvetica, sans-serif;' +
        'Times New Roman/Times New Roman, Times, serif;' +
        '微软雅黑/Microsoft YaHei;' +
        '宋体/SimSun',
    
    // 字体大小（简化版）
    fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;24/24px',
    
    // 图片上传配置
    filebrowserUploadUrl: '/admin/editorImage',
    filebrowserImageUploadUrl: '/admin/editorImage',
    uploadUrl: '/admin/editorImage',
    
    // 内容过滤配置
    allowedContent: true,
    
    // 自动段落
    autoParagraph: false,
    
    // 回车键行为
    enterMode: CKEDITOR.ENTER_P,
    shiftEnterMode: CKEDITOR.ENTER_BR
};

// CKEditor 4 管理器类 - 简化版
class CKEditor4Manager {
    constructor() {
        this.editors = [];
    }

    async initAll(selector) {
        // 等待CKEditor加载完成
        if (typeof CKEDITOR === 'undefined') {
            console.error('CKEditor 4 未加载');
            return;
        }

        const elements = document.querySelectorAll(selector);

        for (const element of elements) {
            try {
                // 为每个元素创建唯一ID
                if (!element.id) {
                    element.id = 'ckeditor4_' + Math.random().toString(36).substring(2, 9);
                }

                const editor = CKEDITOR.replace(element.id, adminEditorConfig);
                
                // 编辑器准备就绪后的处理
                editor.on('instanceReady', (evt) => {
                    const editor = evt.editor;
                    
                    // 设置最小高度
                    editor.ui.space('contents').setStyle('min-height', '300px');
                    
                    // 查找字数统计容器
                    const container = element.closest('.editor-container');
                    const wordCountContainer = container ? 
                        container.querySelector('.editor_container__word-count') : null;
                    
                    if (wordCountContainer) {
                        // 创建字数统计显示
                        const wordCountDiv = document.createElement('div');
                        wordCountDiv.style.cssText = 'padding: 5px; font-size: 12px; color: #666; border-top: 1px solid #ddd;';
                        wordCountContainer.appendChild(wordCountDiv);
                        
                        // 更新字数统计
                        const updateWordCount = () => {
                            const text = editor.getData().replace(/<[^>]*>/g, '');
                            const words = text.trim() ? text.trim().split(/\s+/).length : 0;
                            const chars = text.length;
                            wordCountDiv.textContent = `字数: ${words} | 字符: ${chars}`;
                        };
                        
                        // 监听内容变化
                        editor.on('change', updateWordCount);
                        editor.on('key', updateWordCount);
                        
                        // 初始更新
                        updateWordCount();
                    }
                });

                this.editors.push(editor);
            } catch (error) {
                console.error('Error initializing CKEditor 4:', error);
            }
        }
    }

    // 获取编辑器实例
    getEditor(index = 0) {
        return this.editors[index];
    }

    // 获取所有编辑器
    getAllEditors() {
        return this.editors;
    }

    // 销毁所有编辑器
    destroyAll() {
        this.editors.forEach(editor => {
            if (editor && editor.destroy) {
                editor.destroy();
            }
        });
        this.editors = [];
    }
}

// 获取提及的用户 - 简化版本（暂时返回空数组）
function getMentionedUsers(editor) {
    // 暂时返回空数组，等mention插件安装后再实现
    console.log('getMentionedUsers: mention功能暂未启用');
    return [];
}

// 全局可用
window.CKEditor4Manager = CKEditor4Manager;
window.CKEditorManager = CKEditor4Manager; // 保持兼容性
window.getMentionedUsers = getMentionedUsers;
window.adminEditorConfig = adminEditorConfig;
window.homeEditorConfig = homeEditorConfig;

console.log('CKEditor 4 简化配置已加载');
