/**
 * CKEditor 4 修复版本 - 确保与最新版本兼容
 */

// 等待CKEditor加载完成
function waitForCKEditor(callback) {
    if (typeof CKEDITOR !== 'undefined') {
        callback();
    } else {
        setTimeout(() => waitForCKEditor(callback), 100);
    }
}

// 获取基础配置
function getBaseConfig() {
    return {
        language: 'zh-cn',
        allowedContent: true,
        autoParagraph: false,
        removePlugins: 'elementspath',
        filebrowserUploadUrl: '/admin/editorImage',
        filebrowserImageUploadUrl: '/admin/editorImage'
    };
}

// 获取管理员配置
function getAdminConfig() {
    const baseConfig = getBaseConfig();
    return Object.assign({}, baseConfig, {
        height: 400,
        extraPlugins: 'uploadimage,colorbutton,font,justify,specialchar',
        toolbar: [
            { name: 'document', items: ['Source'] },
            { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
            '/',
            { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', '-', 'RemoveFormat'] },
            { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote'] },
            { name: 'links', items: ['Link', 'Unlink'] },
            '/',
            { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
            { name: 'colors', items: ['TextColor', 'BGColor'] },
            { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar'] },
            { name: 'tools', items: ['Maximize'] }
        ],
        font_names: 'Arial/Arial, Helvetica, sans-serif;Times New Roman/Times New Roman, Times, serif;微软雅黑/Microsoft YaHei;宋体/SimSun;黑体/SimHei',
        fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;24/24px;28/28px;36/36px'
    });
}

// 获取前台配置
function getHomeConfig() {
    const baseConfig = getBaseConfig();
    return Object.assign({}, baseConfig, {
        height: 300,
        extraPlugins: 'uploadimage,colorbutton,font,specialchar',
        toolbar: [
            { name: 'clipboard', items: ['Undo', 'Redo'] },
            { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
            { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'] },
            { name: 'links', items: ['Link', 'Unlink'] },
            { name: 'insert', items: ['Image', 'Table', 'SpecialChar'] },
            { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
            { name: 'colors', items: ['TextColor', 'BGColor'] }
        ],
        font_names: 'Arial/Arial, Helvetica, sans-serif;微软雅黑/Microsoft YaHei;宋体/SimSun',
        fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;24/24px'
    });
}

// CKEditor 4 管理器类
class CKEditor4Manager {
    constructor() {
        this.editors = [];
    }

    async initAll(selector) {
        waitForCKEditor(() => {
            const elements = document.querySelectorAll(selector);

            for (const element of elements) {
                try {
                    // 为每个元素创建唯一ID
                    if (!element.id) {
                        element.id = 'ckeditor4_' + Math.random().toString(36).substring(2, 9);
                    }

                    // 根据页面类型选择配置
                    const config = window.location.pathname.includes('/admin/') ? getAdminConfig() : getHomeConfig();
                    const editor = CKEDITOR.replace(element.id, config);
                    
                    // 编辑器准备就绪后的处理
                    editor.on('instanceReady', (evt) => {
                        const editor = evt.editor;
                        
                        // 设置最小高度
                        editor.ui.space('contents').setStyle('min-height', '300px');
                        
                        // 查找字数统计容器
                        const container = element.closest('.editor-container');
                        const wordCountContainer = container ? 
                            container.querySelector('.editor_container__word-count') : null;
                        
                        if (wordCountContainer) {
                            // 创建字数统计显示
                            const wordCountDiv = document.createElement('div');
                            wordCountDiv.style.cssText = 'padding: 5px; font-size: 12px; color: #666; border-top: 1px solid #ddd;';
                            wordCountContainer.appendChild(wordCountDiv);
                            
                            // 更新字数统计
                            const updateWordCount = () => {
                                const text = editor.getData().replace(/<[^>]*>/g, '');
                                const words = text.trim() ? text.trim().split(/\s+/).length : 0;
                                const chars = text.length;
                                wordCountDiv.textContent = `字数: ${words} | 字符: ${chars}`;
                            };
                            
                            // 监听内容变化
                            editor.on('change', updateWordCount);
                            editor.on('key', updateWordCount);
                            
                            // 初始更新
                            updateWordCount();
                        }
                    });

                    this.editors.push(editor);
                } catch (error) {
                    console.error('Error initializing CKEditor 4:', error);
                }
            }
        });
    }

    // 获取编辑器实例
    getEditor(index = 0) {
        return this.editors[index];
    }

    // 获取所有编辑器
    getAllEditors() {
        return this.editors;
    }

    // 销毁所有编辑器
    destroyAll() {
        this.editors.forEach(editor => {
            if (editor && editor.destroy) {
                editor.destroy();
            }
        });
        this.editors = [];
    }
}

// 获取提及的用户 - 简化版本
function getMentionedUsers(editor) {
    // 暂时返回空数组，mention功能后续添加
    console.log('getMentionedUsers: mention功能暂未启用');
    return [];
}

// 全局可用
window.CKEditor4Manager = CKEditor4Manager;
window.CKEditorManager = CKEditor4Manager; // 保持兼容性
window.getMentionedUsers = getMentionedUsers;

console.log('CKEditor 4 修复版本已加载');
